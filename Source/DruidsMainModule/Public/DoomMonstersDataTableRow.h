#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"

#include "DoomMonstersDataTableRow.gen.cpp"

USTRUCT(BlueprintType)
struct DRUIDS_MAIN_API FDoomMonstersDataTableRow : public FTableRowBase
{
	GENERATED_BODY()

public:
	/** Display name for this entry */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	FString Name;
	
	/** Default constructor */
	FDoomMonstersDataTableRow()
		: Name(TEXT(""))
	{
	}
};