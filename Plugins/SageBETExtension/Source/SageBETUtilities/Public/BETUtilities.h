#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/World.h"
#include "Engine/DataTable.h"
#include "Black_Eye/Public/Components/LookAtComponent.h"

#include "BETUtilities.generated.h"

struct FBlackEyeTarget;

/**
 * Blueprint function library for BET utilities
 */
UCLASS()
class SAGEBETUTILITIES_API UBETUtilities : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static FString GetLookAtSubjectsAsYAML(ULookAtComponent* LookAtComponent);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static void SetLookAtSubjectParams(ULookAtComponent* LookAtComponent, FBlackEyeTarget SubjectParams, int32 SubjectIndex);

private:
};
