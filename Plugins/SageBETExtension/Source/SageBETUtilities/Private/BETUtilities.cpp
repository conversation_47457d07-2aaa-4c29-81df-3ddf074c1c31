#include "BETUtilities.h"
#include "LogDruids.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

FString UBETUtilities::GetLookAtSubjectsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LookAtSubjects:");

	if (IsValid(LookAtComponent))
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (BlackEyeTarget.Actor.IsValid())
			{
				YAMLOutput += FString::Printf(TEXT("\n  -SlotIndex:%d\n    UseComponentBounds:%s\n    BoundingRadius:%f\n    Weight:%f\n    ActorName:%s\n    ComponentName:%s\n    BoneName:%s"),
					i + 1,
					(BlackEyeTarget.bAutoSize ? TEXT("true") : TEXT("false")),
					BlackEyeTarget.BoundingRadius,
					BlackEyeTarget.Weight,
					*BlackEyeTarget.Actor->GetActorNameOrLabel(),
					*BlackEyeTarget.ComponentName,
					*BlackEyeTarget.BoneName);
			}
		}
	}

	YAMLOutput += "\n";
	
	return YAMLOutput;
}

void UBETUtilities::SetLookAtSubjectParams(ULookAtComponent* LookAtComponent, FBlackEyeTarget SubjectParams,
                                           int32 SubjectIndex)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	if (SubjectIndex == 0)
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (!BlackEyeTarget.Actor.IsValid())
			{
				LookAtComponent->SetLookAt(SubjectParams, i, false);
				return;
			}
		}

		return;
	}

	LookAtComponent->SetLookAt(SubjectParams, SubjectIndex - 1, false);
}
